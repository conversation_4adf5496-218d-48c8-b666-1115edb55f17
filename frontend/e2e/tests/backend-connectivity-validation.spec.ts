import { test, expect } from '@playwright/test';
import { BackendConnectivityFix } from '../utils/backend-connectivity-fix';

/**
 * Backend Connectivity Validation Tests
 * 
 * These tests validate that E2E tests can reliably connect to and communicate 
 * with backend services, addressing the specific connectivity issues that were
 * causing test failures.
 */
test.describe('Backend Connectivity Validation', () => {
  let connectivityFix: BackendConnectivityFix;

  test.beforeEach(async ({ page }) => {
    connectivityFix = BackendConnectivityFix.getInstance(page);
  });

  test('should verify backend API endpoints are accessible', async ({ page }) => {
    console.log('🔍 Testing backend API accessibility...');
    
    const result = await connectivityFix.testBackendConnectivity();
    
    // All endpoints should be accessible
    expect(result.healthy).toBe(true);
    expect(result.issues).toHaveLength(0);
    
    // Verify specific endpoints
    expect(result.endpoints.units).toBe(true);
    expect(result.endpoints.entities).toBe(true);
    expect(result.endpoints.connections).toBe(true);
    expect(result.endpoints.health).toBe(true);
    
    console.log('✅ All backend endpoints are accessible');
  });

  test('should validate API stability and response times', async ({ page }) => {
    console.log('🔍 Testing API stability...');
    
    const result = await connectivityFix.testAPIStability();
    
    // API should be stable
    expect(result.stable).toBe(true);
    expect(result.errors).toBeLessThan(2); // Allow up to 1 error out of 10 requests
    expect(result.averageResponseTime).toBeLessThan(1000); // Under 1 second average
    
    console.log(`✅ API is stable with ${result.averageResponseTime.toFixed(0)}ms average response time`);
  });

  test('should create entities without duplicate name conflicts', async ({ page }) => {
    console.log('🔍 Testing entity creation without conflicts...');
    
    // Generate multiple unique entity names
    const entityNames = Array.from({ length: 5 }, (_, i) => 
      connectivityFix.generateUniqueEntityName(`ConflictTest${i}`)
    );
    
    // Verify all names are unique
    const uniqueNames = new Set(entityNames);
    expect(uniqueNames.size).toBe(entityNames.length);
    
    // Create entities sequentially to avoid race conditions
    const result = await connectivityFix.createEntitiesSequentially(entityNames);
    
    // All entities should be created successfully
    expect(result.successful).toHaveLength(entityNames.length);
    expect(result.failed).toHaveLength(0);
    
    console.log(`✅ Created ${result.successful.length} entities without conflicts`);
    
    // Clean up created entities
    for (const entity of result.successful) {
      await connectivityFix.deleteEntitySafely(entity.id);
    }
  });

  test('should perform database cleanup effectively', async ({ page }) => {
    console.log('🔍 Testing database cleanup effectiveness...');
    
    // First, create some test entities
    const testNames = Array.from({ length: 3 }, (_, i) => 
      connectivityFix.generateUniqueEntityName(`CleanupTest${i}`)
    );
    
    const createResult = await connectivityFix.createEntitiesSequentially(testNames);
    expect(createResult.successful).toHaveLength(testNames.length);
    
    // Now test cleanup
    const cleanupResult = await connectivityFix.performDatabaseCleanup();
    
    // Cleanup should succeed
    expect(cleanupResult.entitiesDeleted).toBeGreaterThanOrEqual(testNames.length);
    expect(cleanupResult.errors).toHaveLength(0);
    expect(cleanupResult.duration).toBeLessThan(10000); // Under 10 seconds
    
    console.log(`✅ Cleanup deleted ${cleanupResult.entitiesDeleted} entities in ${cleanupResult.duration}ms`);
  });

  test('should handle API errors gracefully', async ({ page }) => {
    console.log('🔍 Testing API error handling...');
    
    // Try to create an entity with an invalid name
    const result = await connectivityFix.createEntitySafely('Invalid-Name-With-Dashes!');
    
    // Should fail gracefully
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    
    console.log(`✅ API errors handled gracefully: ${result.error}`);
  });

  test('should validate network timeout handling', async ({ page }) => {
    console.log('🔍 Testing network timeout handling...');
    
    // This test verifies that the connectivity fix handles timeouts properly
    // We'll test with a normal request that should succeed quickly
    const start = Date.now();
    const result = await connectivityFix.testBackendConnectivity();
    const duration = Date.now() - start;
    
    // Should complete within reasonable time
    expect(result.healthy).toBe(true);
    expect(duration).toBeLessThan(5000); // Under 5 seconds
    
    console.log(`✅ Connectivity test completed in ${duration}ms`);
  });

  test('should perform comprehensive health check', async ({ page }) => {
    console.log('🔍 Performing comprehensive backend health check...');
    
    const healthCheck = await connectivityFix.performHealthCheck();
    
    // All health indicators should be positive
    expect(healthCheck.healthy).toBe(true);
    expect(healthCheck.connectivity).toBe(true);
    expect(healthCheck.stability).toBe(true);
    expect(healthCheck.cleanup).toBe(true);
    expect(healthCheck.issues).toHaveLength(0);
    
    console.log('✅ Comprehensive health check passed');
  });

  test('should validate worker isolation prevents conflicts', async ({ page }) => {
    console.log('🔍 Testing worker isolation...');
    
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    console.log(`Running as worker: ${workerId}`);
    
    // Generate worker-specific entity names
    const workerNames = Array.from({ length: 3 }, (_, i) => 
      connectivityFix.generateUniqueEntityName(`Worker${workerId}Test${i}`)
    );
    
    // All names should include worker identifier
    workerNames.forEach(name => {
      expect(name).toContain(`W${workerId}`);
    });
    
    // Create entities
    const result = await connectivityFix.createEntitiesSequentially(workerNames);
    expect(result.successful).toHaveLength(workerNames.length);
    
    console.log(`✅ Worker ${workerId} isolation validated with ${result.successful.length} entities`);
    
    // Clean up
    for (const entity of result.successful) {
      await connectivityFix.deleteEntitySafely(entity.id);
    }
  });

  test('should validate CORS and network policy compliance', async ({ page }) => {
    console.log('🔍 Testing CORS and network policies...');
    
    // Test that API calls from the test environment work properly
    // This validates that CORS is properly configured for test requests
    
    try {
      const response = await page.request.get('http://localhost:8000/api/v1/units/', {
        headers: {
          'Origin': 'http://localhost:3000',
          'Content-Type': 'application/json'
        }
      });
      
      expect(response.ok()).toBe(true);
      
      // Check for CORS headers (optional but good to verify)
      const corsHeader = response.headers()['access-control-allow-origin'];
      if (corsHeader) {
        console.log(`CORS header: ${corsHeader}`);
      }
      
      console.log('✅ CORS and network policies are properly configured');
      
    } catch (error) {
      console.error('❌ CORS or network policy issue:', error);
      throw error;
    }
  });

  test('should validate authentication handling', async ({ page }) => {
    console.log('🔍 Testing authentication handling...');
    
    // Since the MVP doesn't require authentication, we're testing that
    // requests work without authentication requirements causing issues
    
    const result = await connectivityFix.testBackendConnectivity();
    expect(result.healthy).toBe(true);
    
    // Test that we can perform CRUD operations without auth issues
    const entityName = connectivityFix.generateUniqueEntityName('AuthTest');
    const createResult = await connectivityFix.createEntitySafely(entityName);
    
    expect(createResult.success).toBe(true);
    
    if (createResult.entity) {
      const deleteResult = await connectivityFix.deleteEntitySafely(createResult.entity.id);
      expect(deleteResult.success).toBe(true);
    }
    
    console.log('✅ Authentication handling validated (no auth required for MVP)');
  });
});