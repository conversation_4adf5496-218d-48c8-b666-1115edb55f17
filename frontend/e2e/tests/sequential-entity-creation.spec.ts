import { test, expect } from '@playwright/test';
import { EntityManagerPage } from '../fixtures/page-objects';
import { TestHelpers } from '../utils/helpers';

test.describe('Sequential Entity Creation Test', () => {
  let entityPage: EntityManagerPage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.setupDialogHandler();
    await helpers.cleanupBeforeTest();
    
    entityPage = new EntityManagerPage(page);
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
  });

  test.afterEach(async ({ page }) => {
    await helpers.cleanupAfterTest();
  });

  test('should create multiple entities sequentially without state interference', async ({ page }) => {
    const entities = [
      helpers.generateUniqueEntityName('First Entity'),
      helpers.generateUniqueEntityName('Second Entity'),
      helpers.generateUniqueEntityName('Third Entity'),
      helpers.generateUniqueEntityName('Fourth Entity')
    ];

    console.log('Testing sequential entity creation with enhanced state management...');

    for (let i = 0; i < entities.length; i++) {
      const entityName = entities[i];
      console.log(`\n=== Creating entity ${i + 1}/4: ${entityName} ===`);
      
      const startTime = Date.now();
      
      try {
        await helpers.createAndTrackEntityWithId(entityPage, entityName);
        const duration = Date.now() - startTime;
        console.log(`✅ Successfully created entity ${i + 1}: ${entityName} in ${duration}ms`);
        
        // Verify entity appears in the list
        await expect(page.locator(`:text("${entityName}")`)).toBeVisible();
        
        // Add small delay between entities to simulate realistic usage
        await page.waitForTimeout(200);
        
      } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`❌ Failed to create entity ${i + 1}: ${entityName} after ${duration}ms`);
        console.error(`Error: ${error.message}`);
        throw error;
      }
    }

    console.log('\n🎉 All entities created successfully!');
    
    // Verify all entities are visible in the final list
    for (const entityName of entities) {
      await expect(page.locator(`:text("${entityName}")`)).toBeVisible();
    }
  });

  test('should handle rapid entity creation (stress test)', async ({ page }) => {
    const entities = [
      helpers.generateUniqueEntityName('Rapid A'),
      helpers.generateUniqueEntityName('Rapid B'),
      helpers.generateUniqueEntityName('Rapid C')
    ];

    console.log('Testing rapid sequential entity creation...');

    // Create entities with enhanced state management for stress testing
    for (let i = 0; i < entities.length; i++) {
      const entityName = entities[i];
      console.log(`Creating: ${entityName}`);
      
      // Enhanced pre-creation state verification
      await page.waitForLoadState('networkidle');
      await helpers.waitForAppReady();
      
      // Create entity with improved tracking
      await helpers.createAndTrackEntityWithId(entityPage, entityName);
      
      // Enhanced verification with immediate visibility check
      await expect(page.locator(`:text("${entityName}")`)).toBeVisible({ timeout: 5000 });
      
      // Progressive delay strategy for better state isolation
      const delay = Math.max(100, 50 + (i * 25)); // Minimum 100ms, increasing by 25ms per entity
      console.log(`  ✓ Created and tracked entity: ${entityName} (ID: temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}) in ${delay}ms wait`);
      await page.waitForTimeout(delay);
      
      // Ensure form is completely cleared before next iteration
      if (await entityPage.entityForm.isVisible().catch(() => false)) {
        console.log(`  Warning: Form still visible after entity ${i + 1}, ensuring cleanup...`);
        await entityPage.cancelForm();
        await page.waitForTimeout(100);
      }
    }

    console.log('Rapid creation test completed successfully!');
    
    // Final comprehensive verification
    console.log('Performing final verification of all entities...');
    for (const entityName of entities) {
      await expect(page.locator(`:text("${entityName}")`)).toBeVisible({ timeout: 3000 });
    }
  });
});