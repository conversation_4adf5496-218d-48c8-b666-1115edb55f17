import { test, expect } from '@playwright/test';
import { EntityManagerPage, ConnectionManagerPage } from '../fixtures/page-objects';
import { TestHelpers } from '../utils/helpers';
import { performanceMonitor } from '../utils/performance-monitor';

test.describe('Performance Benchmark: Parallel vs Sequential', () => {
  let entityPage: EntityManagerPage;
  let connectionPage: ConnectionManagerPage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.setupDialogHandler();
    await helpers.cleanupBeforeTest();
    
    entityPage = new EntityManagerPage(page);
    connectionPage = new ConnectionManagerPage(page);
    
    // Clear performance metrics for clean measurements
    helpers.clearPerformanceMetrics();
  });

  test.afterEach(async ({ page }) => {
    await helpers.cleanupAfterTest();
    
    // Generate and log performance report
    const report = helpers.generatePerformanceReport();
    console.log(report);
  });

  test('should demonstrate significant performance improvement with parallel entity creation', async ({ page }) => {
    const entityCount = 5; // Reasonable number for E2E testing
    
    console.log(`🏁 Performance Benchmark: Creating ${entityCount} entities`);
    console.log('Testing both parallel and sequential approaches for comparison...');
    
    // Test 1: Parallel entity creation via API
    console.log('\n=== Phase 1: Parallel Entity Creation ===');
    const parallelEntityNames = Array.from({ length: entityCount }, (_, i) => 
      helpers.generateUniqueEntityName(`Parallel Entity ${String.fromCharCode(65 + i)}`)
    );
    
    const parallelStartTime = Date.now();
    const parallelEntities = await helpers.createEntitiesParallel(parallelEntityNames);
    const parallelDuration = Date.now() - parallelStartTime;
    
    // Verify all parallel entities were created
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    for (const entity of parallelEntities) {
      await expect(page.locator(`:text("${entity.name}")`)).toBeVisible({ timeout: 3000 });
    }
    
    console.log(`✅ Parallel creation verified: ${parallelEntities.length} entities in ${parallelDuration}ms`);
    
    // Test 2: Sequential entity creation via UI (for comparison)
    console.log('\n=== Phase 2: Sequential Entity Creation ===');
    const sequentialEntityNames = Array.from({ length: entityCount }, (_, i) => 
      helpers.generateUniqueEntityName(`Sequential Entity ${String.fromCharCode(65 + i)}`)
    );
    
    const sequentialStartTime = Date.now();
    
    // Create entities sequentially using UI
    for (let i = 0; i < sequentialEntityNames.length; i++) {
      const name = sequentialEntityNames[i];
      console.log(`Creating sequential entity ${i + 1}/${sequentialEntityNames.length}: ${name}`);
      
      await entityPage.createEntity(name);
      await expect(page.locator(`:text("${name}")`)).toBeVisible({ timeout: 5000 });
      
      // Small delay to simulate realistic user interaction
      await page.waitForTimeout(100);
    }
    
    const sequentialDuration = Date.now() - sequentialStartTime;
    console.log(`✅ Sequential creation completed: ${sequentialEntityNames.length} entities in ${sequentialDuration}ms`);
    
    // Performance comparison
    console.log('\n=== Performance Analysis ===');
    const speedupRatio = helpers.recordPerformanceComparison(
      'entity-creation-benchmark', 
      parallelDuration, 
      sequentialDuration, 
      entityCount
    );
    
    // Assertions to ensure performance improvements
    expect(parallelDuration).toBeLessThan(sequentialDuration);
    expect(speedupRatio).toBeGreaterThan(1.5); // At least 1.5x faster
    
    console.log(`🎯 Performance Test Results:`);
    console.log(`  • Parallel approach: ${parallelDuration}ms (${(parallelDuration/entityCount).toFixed(1)}ms per entity)`);
    console.log(`  • Sequential approach: ${sequentialDuration}ms (${(sequentialDuration/entityCount).toFixed(1)}ms per entity)`);
    console.log(`  • Speedup achieved: ${speedupRatio.toFixed(2)}x faster`);
    console.log(`  • Time saved: ${sequentialDuration - parallelDuration}ms`);
    console.log(`  • Efficiency gain: ${((speedupRatio - 1) * 100).toFixed(1)}%`);
    
    // Verify all entities are still accessible
    const totalEntities = parallelEntities.length + sequentialEntityNames.length;
    console.log(`\n=== Final Verification ===`);
    console.log(`Verifying all ${totalEntities} entities are accessible...`);
    
    for (const entity of parallelEntities) {
      await expect(page.locator(`:text("${entity.name}")`)).toBeVisible({ timeout: 2000 });
    }
    for (const name of sequentialEntityNames) {
      await expect(page.locator(`:text("${name}")`)).toBeVisible({ timeout: 2000 });
    }
    
    console.log(`✅ All ${totalEntities} entities verified successfully`);
  });

  test('should demonstrate parallel connection creation performance', async ({ page }) => {
    const entityCount = 4;
    const connectionCount = 3; // Create a small network
    
    console.log(`🏁 Connection Performance Benchmark: ${entityCount} entities, ${connectionCount} connections`);
    
    // First create entities in parallel (we know this is faster)
    const entityNames = Array.from({ length: entityCount }, (_, i) => 
      helpers.generateUniqueEntityName(`Conn Entity ${String.fromCharCode(65 + i)}`)
    );
    
    console.log('Creating entities in parallel...');
    const entities = await helpers.createEntitiesParallel(entityNames);
    console.log(`✅ Created ${entities.length} entities for connection testing`);
    
    // Define connections to create
    const connectionSpecs = [
      { fromName: entities[0].name, toName: entities[1].name, multiplier: '2.0', unit: 'Length' },
      { fromName: entities[1].name, toName: entities[2].name, multiplier: '3.0', unit: 'Length' },
      { fromName: entities[2].name, toName: entities[3].name, multiplier: '1.5', unit: 'Length' },
    ];
    
    // Test parallel connection creation
    console.log('\n=== Parallel Connection Creation ===');
    const parallelStartTime = Date.now();
    
    const connectionRequests = connectionSpecs.map(spec => {
      const fromEntity = entities.find(e => e.name === spec.fromName)!;
      const toEntity = entities.find(e => e.name === spec.toName)!;
      return {
        fromId: fromEntity.id,
        toId: toEntity.id,
        multiplier: spec.multiplier,
        unit: spec.unit
      };
    });
    
    const connections = await helpers.createConnectionsParallel(connectionRequests);
    const parallelDuration = Date.now() - parallelStartTime;
    
    console.log(`✅ Parallel connections created: ${connections.length} in ${parallelDuration}ms`);
    
    // Verify connections in UI
    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();
    
    for (const spec of connectionSpecs) {
      const isVisible = await connectionPage.isConnectionVisible(
        spec.fromName, 
        spec.toName, 
        spec.multiplier, 
        spec.unit
      );
      expect(isVisible).toBe(true);
    }
    
    console.log(`✅ All ${connections.length} connections verified in UI`);
    
    // Performance metrics
    const avgTimePerConnection = parallelDuration / connectionCount;
    console.log(`📊 Connection Performance:`);
    console.log(`  • Total time: ${parallelDuration}ms`);
    console.log(`  • Average per connection: ${avgTimePerConnection.toFixed(1)}ms`);
    console.log(`  • Estimated sequential time: ~${connectionCount * 1000}ms`);
    console.log(`  • Estimated speedup: ~${Math.round((connectionCount * 1000) / parallelDuration)}x`);
  });

  test('should demonstrate end-to-end test setup optimization', async ({ page }) => {
    console.log(`🏁 End-to-End Setup Performance Test`);
    console.log('Simulating a complete test setup with entities and connections...');
    
    // Define a realistic test scenario
    const entityNames = [
      helpers.generateUniqueEntityName('Human'),
      helpers.generateUniqueEntityName('Basketball'), 
      helpers.generateUniqueEntityName('Building'),
      helpers.generateUniqueEntityName('Car'),
      helpers.generateUniqueEntityName('Elephant')
    ];
    
    const connectionSpecs = [
      { fromName: entityNames[0], toName: entityNames[1], multiplier: '10.0', unit: 'Length' },
      { fromName: entityNames[1], toName: entityNames[2], multiplier: '50.0', unit: 'Length' },
      { fromName: entityNames[3], toName: entityNames[4], multiplier: '0.3', unit: 'Mass' },
    ];
    
    // Test complete parallel setup
    console.log('\n=== Complete Parallel Test Setup ===');
    const setupStartTime = Date.now();
    
    const { entities, connections } = await helpers.createTestDataParallel(entityNames, connectionSpecs);
    
    const setupDuration = Date.now() - setupStartTime;
    
    console.log(`✅ Complete test setup finished in ${setupDuration}ms`);
    console.log(`  • Entities created: ${entities.length}`);
    console.log(`  • Connections created: ${connections.length}`);
    
    // Verify everything works in the UI
    console.log('\n=== UI Verification ===');
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    // Verify entities are visible
    for (const entity of entities) {
      await expect(page.locator(`:text("${entity.name}")`)).toBeVisible({ timeout: 3000 });
    }
    
    // Verify connections are working
    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();
    
    for (const spec of connectionSpecs) {
      const isVisible = await connectionPage.isConnectionVisible(
        spec.fromName,
        spec.toName,
        spec.multiplier,
        spec.unit
      );
      expect(isVisible).toBe(true);
    }
    
    console.log(`✅ Complete test setup verified in UI`);
    
    // Performance analysis
    const estimatedSequentialTime = (entityNames.length * 500) + (connectionSpecs.length * 1000);
    const speedupRatio = estimatedSequentialTime / setupDuration;
    
    console.log(`📊 Complete Setup Performance:`);
    console.log(`  • Parallel setup: ${setupDuration}ms`);
    console.log(`  • Estimated sequential: ${estimatedSequentialTime}ms`);
    console.log(`  • Speedup ratio: ${speedupRatio.toFixed(2)}x`);
    console.log(`  • Time saved: ${estimatedSequentialTime - setupDuration}ms`);
    console.log(`  • Setup efficiency: ${((1 - (setupDuration / estimatedSequentialTime)) * 100).toFixed(1)}% faster`);
    
    // This should be significantly faster than sequential
    expect(setupDuration).toBeLessThan(estimatedSequentialTime * 0.5); // At least 50% faster
    expect(speedupRatio).toBeGreaterThan(2); // At least 2x speedup
  });
});