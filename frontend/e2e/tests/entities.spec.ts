import { test, expect } from '@playwright/test';
import { EntityManagerPage, ValidationTimeouts } from '../fixtures/page-objects';
import { TestHelpers } from '../utils/helpers';
import { validEntityNames } from '../fixtures/test-data';

test.describe('Entity Management', () => {
  let entityPage: EntityManagerPage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.setupDialogHandler();
    
    // Clean up any leftover test data from previous runs
    await helpers.cleanupBeforeTest();
    
    // Set up network error monitoring
    page.on('requestfailed', request => {
      if (request.url().includes('/api/v1/entities')) {
        console.error(`Failed entity API request: ${request.url()} - ${request.failure()?.errorText}`);
      }
    });
    
    // Set up console error monitoring
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.error(`Browser error: ${msg.text()}`);
      }
    });
    
    entityPage = new EntityManagerPage(page);
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
  });

  test.afterEach(async ({ page }) => {
    // Clean up any test entities created during this test
    await helpers.cleanupAfterTest();
  });

  test('should display entity management page correctly', async ({ page }) => {
    await expect(page.locator('h2:has-text("Entity Management")')).toBeVisible();
    await expect(entityPage.createButton).toBeVisible();
    await expect(entityPage.entityList).toBeVisible();
  });

  test('should create a new entity successfully', async ({ page }) => {
    const entityName = helpers.generateUniqueEntityName('Test Entity');
    
    await helpers.createAndTrackEntityWithId(entityPage, entityName);
    
    // Verify entity appears in list
    await expect(page.locator(`:text("${entityName}")`)).toBeVisible();
    
    // Verify form is removed from DOM after successful creation
    await expect(entityPage.entityForm).not.toBeAttached();
  });

  test('should show and hide entity form correctly', async ({ page }) => {
    // Initially form should not be in DOM
    await expect(entityPage.entityForm).not.toBeAttached();
    await expect(entityPage.createButton).toBeVisible();
    
    // Click create button should show form
    await entityPage.clickCreateNew();
    await expect(entityPage.entityForm).toBeVisible();
    await expect(entityPage.createButton).toBeHidden();
    
    // Cancel should remove form from DOM (handled by cancelForm method)
    await entityPage.cancelForm();
    await expect(entityPage.createButton).toBeVisible();
  });

  test('should validate entity name requirements with real-time validation', async ({ page }) => {
    // Test empty string with real-time validation
    await entityPage.clickCreateNew();
    
    // Wait for React component to fully initialize
    await entityPage.nameInput.waitFor({ state: 'visible', timeout: 5000 });
    
    // Type something first to trigger touched state, then clear - exact match to debug test
    await entityPage.nameInput.fill('test');
    await entityPage.nameInput.waitFor({ state: 'attached' });
    await entityPage.nameInput.fill('');
    await entityPage.nameInput.waitFor({ state: 'attached' });
    await entityPage.nameInput.blur(); // Trigger validation
    
    // Use browser-specific timeout for validation completion
    const validationTimeout = ValidationTimeouts.getBrowserValidationTimeout(page);
    console.log(`Using browser-specific validation timeout: ${validationTimeout}ms`);
    
    // Enhanced functional validation check instead of CSS class timing
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const errorMsg = document.querySelector('[data-testid="entity-form-error"]');
      
      if (!input || !submitBtn) return false;
      
      // For empty input, expect: invalid state, disabled button, error message
      const isEmpty = input.value.trim() === '';
      const isDisabled = submitBtn.disabled;
      const hasError = errorMsg && errorMsg.textContent && errorMsg.textContent.includes('required');
      const ariaInvalid = input.getAttribute('aria-invalid') === 'true';
      
      return isEmpty && isDisabled && hasError && ariaInvalid;
    }, { timeout: validationTimeout });
    
    // Should show required field error - test aria-invalid first since that's working
    await expect(entityPage.nameInput).toHaveAttribute('aria-invalid', 'true');
    await expect(entityPage.submitButton).toBeDisabled();
    
    // Verify error message with browser-specific timeout
    const errorTimeout = ValidationTimeouts.getBrowserErrorTimeout(page);
    await expect(page.locator('[data-testid="entity-form-error"]')).toBeVisible({ timeout: errorTimeout });
    await expect(page.locator('[data-testid="entity-form-error"]')).toContainText(/required/i);
    await expect(entityPage.nameInput).toHaveClass(/invalid/);
    
    await entityPage.cancelForm();
    
    // Test maximum length (20 chars) - should be valid
    await entityPage.clickCreateNew();
    await entityPage.nameInput.fill('A'.repeat(20)); // Exactly 20 characters
    await entityPage.nameInput.blur(); // Trigger validation
    
    // Use browser-specific timeout for valid state detection
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      
      if (!input || !submitBtn) return false;
      
      // For valid 20-char input: enabled button, valid state
      const hasValidLength = input.value.length === 20;
      const isEnabled = !submitBtn.disabled;
      const ariaValid = input.getAttribute('aria-invalid') === 'false';
      
      return hasValidLength && isEnabled && ariaValid;
    }, { timeout: validationTimeout });
    
    // Should show valid state (maxLength prevents >20 chars)
    await expect(entityPage.errorMessage).not.toBeVisible();
    await expect(entityPage.submitButton).toBeEnabled({ timeout: ValidationTimeouts.getBrowserButtonTimeout(page) });
    await expect(entityPage.nameInput).toHaveClass(/valid/);
    await expect(entityPage.nameInput).toHaveAttribute('aria-invalid', 'false');
    
    // Should show success indicator with multiple selector fallbacks
    await expect(page.locator('.validation-success, :text("✓"), [aria-live="polite"]')).toBeVisible();
    await entityPage.cancelForm();
    
    // Test invalid characters with real-time validation
    await entityPage.clickCreateNew();
    await entityPage.nameInput.fill('Test123!');
    await entityPage.nameInput.blur(); // Trigger validation
    
    // Use functional validation for invalid pattern
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const errorMsg = document.querySelector('[data-testid="entity-form-error"]');
      
      if (!input || !submitBtn) return false;
      
      // For invalid pattern input: disabled button, error message about pattern
      const hasInvalidPattern = !/^[a-zA-Z\s]+$/.test(input.value);
      const isDisabled = submitBtn.disabled;
      const hasPatternError = errorMsg && errorMsg.textContent && errorMsg.textContent.includes('letters');
      const ariaInvalid = input.getAttribute('aria-invalid') === 'true';
      
      return hasInvalidPattern && isDisabled && hasPatternError && ariaInvalid;
    }, { timeout: validationTimeout });
    
    // Should show pattern error immediately
    await expect(entityPage.errorMessage).toBeVisible({ timeout: errorTimeout });
    await expect(entityPage.errorMessage).toContainText(/letters and spaces/i);
    await expect(entityPage.submitButton).toBeDisabled();
    
    // Input should have invalid styling
    await expect(entityPage.nameInput).toHaveClass(/invalid/);
    await entityPage.cancelForm();
    
    // Test valid input shows success state
    await entityPage.clickCreateNew();
    await entityPage.nameInput.fill('Valid Entity Name');
    await entityPage.nameInput.blur(); // Trigger validation
    
    // Use functional validation for valid input
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      
      if (!input || !submitBtn) return false;
      
      // For valid input: enabled button, no error, valid state
      const hasValidPattern = /^[a-zA-Z\s]+$/.test(input.value);
      const hasValidLength = input.value.length > 0 && input.value.length <= 20;
      const isEnabled = !submitBtn.disabled;
      const ariaValid = input.getAttribute('aria-invalid') === 'false';
      
      return hasValidPattern && hasValidLength && isEnabled && ariaValid;
    }, { timeout: validationTimeout });
    
    // Should show valid state
    await expect(entityPage.errorMessage).not.toBeVisible();
    await expect(entityPage.submitButton).toBeEnabled({ timeout: ValidationTimeouts.getBrowserButtonTimeout(page) });
    await expect(entityPage.nameInput).toHaveClass(/valid/);
    await expect(entityPage.nameInput).toHaveAttribute('aria-invalid', 'false');
    
    // Should show success indicator with enhanced selectors
    await expect(page.locator('.validation-success, :text("✓"), [aria-live="polite"]')).toBeVisible();
    await entityPage.cancelForm();
  });

  test('should accept valid entity names', async ({ page }) => {
    for (let i = 0; i < validEntityNames.length; i++) {
      const validName = validEntityNames[i];
      const uniqueName = helpers.generateUniqueEntityName(validName);
      
      console.log(`Testing valid name ${i + 1}/${validEntityNames.length}: ${uniqueName}`);
      
      // Enhanced state verification before each creation
      await page.waitForLoadState('networkidle');
      await helpers.waitForAppReady();
      
      await entityPage.createEntity(uniqueName);
      
      // Should not show error and entity should be created
      await expect(entityPage.errorMessage).not.toBeVisible();
      await expect(page.locator(`:text("${uniqueName}")`)).toBeVisible();
      
      // Progressive delay for sequential operations
      const delay = 200 + (i * 50); // Increasing delay for better state isolation
      await page.waitForTimeout(delay);
      
      // Ensure form is clean before next iteration
      await expect(entityPage.entityForm).not.toBeAttached();
      await expect(entityPage.createButton).toBeVisible();
    }
  });

  test('should prevent duplicate entity names', async ({ page }) => {
    const entityName = helpers.generateUniqueEntityName('Duplicate Test');
    
    // Create first entity
    await entityPage.createEntity(entityName);
    await expect(page.locator(`:text("${entityName}")`)).toBeVisible();
    
    // Try to create duplicate (expect failure)
    // The enhanced createEntity method now waits for the error message when expectFailure=true
    await entityPage.createEntity(entityName, true);
    
    // Enhanced error message detection with multiple selector fallbacks
    // and longer timeout to handle API delay + React state propagation
    const errorSelectors = [
      '[data-testid="entity-form-error"]',
      '.error-message',
      '.error', 
      '[role="alert"]',
      '.alert-error'
    ];
    
    // Try multiple selectors for robust error detection
    let errorFound = false;
    let errorText = '';
    
    for (const selector of errorSelectors) {
      try {
        const errorElement = page.locator(selector);
        await errorElement.waitFor({ state: 'visible', timeout: 3000 });
        errorText = await errorElement.textContent() || '';
        
        if (errorText.trim() && /already exists|duplicate/i.test(errorText)) {
          errorFound = true;
          console.log(`Duplicate error found with selector ${selector}: "${errorText.trim()}"`);
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }
    
    // Fallback verification using the enhanced error detection
    if (!errorFound) {
      console.log('Standard selectors failed, using enhanced error detection...');
      const duplicateErrorText = await entityPage.waitForDuplicateError(entityName, 8000);
      if (duplicateErrorText) {
        errorFound = true;
        errorText = duplicateErrorText;
      }
    }
    
    // Verify error message appeared - always expect it to be found
    if (!errorFound) {
      // Enhanced debugging for failed error detection
      console.error('ERROR: No duplicate error message found!');
      console.error(`Attempted entity name: "${entityName}"`);
      
      // Debug: Check what error messages are actually visible
      const allErrors = await page.locator('[data-testid*="error"], .error, .error-message, [role="alert"]').allTextContents();
      console.error('All error messages found:', allErrors);
      
      // Debug: Check form state
      const formVisible = await entityPage.entityForm.isVisible();
      const buttonState = await entityPage.submitButton.isEnabled();
      const inputValue = await entityPage.nameInput.inputValue();
      console.error(`Form state: visible=${formVisible}, button enabled=${buttonState}, input="${inputValue}"`);
      
      // Force the test to fail with descriptive message
      throw new Error(`Duplicate entity error message not found. Expected error for entity "${entityName}" but no duplicate/already exists error appeared. Available errors: ${allErrors.join(', ')}`);
    }
    
    // Use the working error selector for the expectation
    await expect(entityPage.errorMessage).toBeVisible({ timeout: 2000 });
    await expect(entityPage.errorMessage).toContainText(/already exists|duplicate/i);
    
    // Form should still be visible after error
    await expect(entityPage.entityForm).toBeVisible();
  });

  test('should delete an entity successfully', async ({ page }) => {
    const entityName = helpers.generateUniqueEntityName('Delete Me');
    
    // Create entity first
    await entityPage.createEntity(entityName);
    await expect(page.locator(`:text("${entityName}")`)).toBeVisible();
    
    // Delete the entity
    await entityPage.deleteEntity(entityName);
    
    // Wait a moment for deletion to process
    await page.waitForTimeout(500);
    
    // Verify entity is removed
    await expect(page.locator(`:text("${entityName}")`)).not.toBeVisible();
  });

  test('should edit an entity successfully', async ({ page }) => {
    const originalName = helpers.generateUniqueEntityName('Edit Original');
    const newName = helpers.generateUniqueEntityName('Edit Updated');
    
    // Create entity first
    await entityPage.createEntity(originalName);
    await expect(page.locator(`:text("${originalName}")`)).toBeVisible();
    
    // Edit the entity
    await entityPage.editEntity(originalName, newName);
    
    // Verify old name is gone and new name appears
    await expect(page.locator(`:text("${originalName}")`)).not.toBeVisible();
    await expect(page.locator(`:text("${newName}")`)).toBeVisible();
  });

  test('should handle entity form validation for name length', async ({ page }) => {
    await entityPage.clickCreateNew();
    
    // Test maximum length (20 characters should be OK)
    const maxLengthName = 'A'.repeat(20);
    await entityPage.nameInput.fill(maxLengthName);
    await entityPage.nameInput.blur(); // Trigger validation before submit
    
    // Use browser-specific timeout for validation
    const validationTimeout = ValidationTimeouts.getBrowserValidationTimeout(page);
    
    // Wait for validation to complete with browser-specific timing
    await page.waitForFunction(() => {
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      
      if (!submitBtn || !input) {
        console.log('Test validation: Missing elements', { submitBtn: !!submitBtn, input: !!input });
        return false;
      }
      
      console.log('Test validation: Button state', {
        inputValue: input.value,
        inputLength: input.value.length,
        buttonDisabled: submitBtn.disabled,
        buttonText: submitBtn.textContent,
        ariaInvalid: input.getAttribute('aria-invalid')
      });
      
      // For valid 20-char input: should be enabled
      const result = input.value.length === 20 && !submitBtn.disabled;
      console.log('Test validation: Result', result);
      return result;
    }, { timeout: validationTimeout });
    
    // Use enhanced submit button interaction for successful case
    await entityPage.waitForSubmitButtonReady();
    await entityPage.clickSubmitButtonWithRetry();
    
    // Should not show error for max length - wait briefly for any error to appear
    await page.waitForTimeout(500); // Brief wait to ensure no error appears
    await expect(entityPage.errorMessage).not.toBeVisible();
    
    // Cancel and test over-length
    await entityPage.cancelForm();
    await entityPage.clickCreateNew();
    
    const overLengthName = 'A'.repeat(21);
    await entityPage.nameInput.fill(overLengthName);
    await entityPage.nameInput.blur(); // Trigger validation
    
    // Wait for validation error to appear with browser-specific timing
    await page.waitForFunction(() => {
      const errorMsg = document.querySelector('[data-testid="entity-form-error"], .error-message, [role="alert"]');
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      
      if (!errorMsg || !submitBtn || !input) return false;
      
      // For over-length input: should be disabled and show error
      return input.value.length === 21 && submitBtn.disabled && errorMsg.textContent;
    }, { timeout: validationTimeout });
    
    // Verify button is disabled and error is shown (no click needed)
    await expect(entityPage.submitButton).toBeDisabled();
    
    // Should show error for over-length
    const errorTimeout = ValidationTimeouts.getBrowserErrorTimeout(page);
    await expect(entityPage.errorMessage).toBeVisible({ timeout: errorTimeout });
  });

  test('should handle entity form validation for special characters with real-time feedback', async ({ page }) => {
    const invalidNames = [
      'Test123',      // Numbers
      'Test!@#',      // Special chars
      'Test_Entity',  // Underscore
      'Test-Entity',  // Hyphen
    ];
    
    const validationTimeout = ValidationTimeouts.getBrowserValidationTimeout(page);
    const errorTimeout = ValidationTimeouts.getBrowserErrorTimeout(page);
    
    for (const invalidName of invalidNames) {
      console.log(`Testing invalid name: ${invalidName}`);
      await entityPage.clickCreateNew();
      await entityPage.nameInput.fill(invalidName);
      await entityPage.nameInput.blur(); // Trigger real-time validation
      
      // Enhanced wait for validation state with browser-specific timing
      await page.waitForFunction(() => {
        const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
        const errorMsg = document.querySelector('[data-testid="entity-form-error"], .error-message, [role="alert"]');
        const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
        
        if (!input || !errorMsg || !submitBtn) return false;
        
        // For invalid pattern: should have invalid class, error message, disabled button
        const hasInvalidPattern = !/^[a-zA-Z\s]+$/.test(input.value);
        const hasInvalidClass = input.classList.contains('invalid');
        const hasErrorText = errorMsg.textContent && errorMsg.textContent.includes('letters');
        const isDisabled = submitBtn.disabled;
        const ariaInvalid = input.getAttribute('aria-invalid') === 'true';
        
        return hasInvalidPattern && hasInvalidClass && hasErrorText && isDisabled && ariaInvalid;
      }, { timeout: validationTimeout });
      
      // Should show validation error immediately
      await expect(entityPage.errorMessage).toBeVisible({ timeout: errorTimeout });
      await expect(entityPage.errorMessage).toContainText(/letters and spaces/i);
      await expect(entityPage.submitButton).toBeDisabled();
      await expect(entityPage.nameInput).toHaveClass(/invalid/);
      
      await entityPage.cancelForm();
    }
  });

  test('should provide real-time validation feedback during typing', async ({ page }) => {
    await entityPage.clickCreateNew();
    
    const validationTimeout = ValidationTimeouts.getBrowserValidationTimeout(page);
    const buttonTimeout = ValidationTimeouts.getBrowserButtonTimeout(page);
    const errorTimeout = ValidationTimeouts.getBrowserErrorTimeout(page);
    
    // Test real-time validation on change events
    await entityPage.nameInput.fill('Valid Name');
    await entityPage.nameInput.blur();
    
    // Enhanced wait for initial valid state with browser-specific timing
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      
      if (!input || !submitBtn) return false;
      
      // For valid input: should have valid class and enabled button
      const hasValidPattern = /^[a-zA-Z\s]+$/.test(input.value);
      const hasValidClass = input.classList.contains('valid');
      const isEnabled = !submitBtn.disabled;
      const ariaValid = input.getAttribute('aria-invalid') === 'false';
      
      return hasValidPattern && hasValidClass && isEnabled && ariaValid;
    }, { timeout: validationTimeout });
    
    // Should show valid state
    await expect(entityPage.nameInput).toHaveClass(/valid/);
    await expect(entityPage.submitButton).toBeEnabled({ timeout: buttonTimeout });
    
    // Now type invalid characters - should update in real-time
    await entityPage.nameInput.fill('Valid Name123');
    
    // Enhanced wait for real-time validation change with browser-specific timing
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const errorMsg = document.querySelector('[data-testid="entity-form-error"], .error-message, [role="alert"]');
      
      if (!input || !submitBtn) return false;
      
      // For invalid input: should have invalid class, disabled button, error message
      const hasInvalidPattern = !/^[a-zA-Z\s]+$/.test(input.value);
      const hasInvalidClass = input.classList.contains('invalid');
      const isDisabled = submitBtn.disabled;
      const hasError = errorMsg && errorMsg.textContent;
      const ariaInvalid = input.getAttribute('aria-invalid') === 'true';
      
      return hasInvalidPattern && hasInvalidClass && isDisabled && hasError && ariaInvalid;
    }, { timeout: validationTimeout });
    
    // Should show invalid state without needing to blur
    await expect(entityPage.nameInput).toHaveClass(/invalid/);
    await expect(entityPage.submitButton).toBeDisabled();
    await expect(entityPage.errorMessage).toBeVisible({ timeout: errorTimeout });
    
    // Fix the error by removing invalid characters
    await entityPage.nameInput.fill('Valid Name');
    
    // Enhanced wait for validation recovery with browser-specific timing
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const errorMsg = document.querySelector('[data-testid="entity-form-error"], .error-message, [role="alert"]');
      
      if (!input || !submitBtn) return false;
      
      // For recovered valid input: should have valid class, enabled button, no error
      const hasValidPattern = /^[a-zA-Z\s]+$/.test(input.value);
      const hasValidClass = input.classList.contains('valid');
      const isEnabled = !submitBtn.disabled;
      const noError = !errorMsg || !errorMsg.textContent || errorMsg.style.display === 'none';
      const ariaValid = input.getAttribute('aria-invalid') === 'false';
      
      return hasValidPattern && hasValidClass && isEnabled && noError && ariaValid;
    }, { timeout: validationTimeout });
    
    // Should return to valid state
    await expect(entityPage.nameInput).toHaveClass(/valid/);
    await expect(entityPage.submitButton).toBeEnabled({ timeout: buttonTimeout });
    await expect(entityPage.errorMessage).not.toBeVisible();
    
    await entityPage.cancelForm();
  });

  test('should handle empty entity list gracefully', async ({ page }) => {
    // If there are no entities, the page should still render correctly
    await expect(entityPage.entityList).toBeVisible();
    await expect(entityPage.createButton).toBeVisible();
    
    // Should show some indication of empty state or just render empty list
    // This depends on your implementation
  });

  test('should maintain entity list after page refresh', async ({ page }) => {
    const entityName = helpers.generateUniqueEntityName('Refresh Test');
    
    // Create entity
    await entityPage.createEntity(entityName);
    await expect(page.locator(`:text("${entityName}")`)).toBeVisible();
    
    // Refresh page
    await page.reload();
    await helpers.waitForAppReady();
    
    // Entity should still be visible
    await expect(page.locator(`:text("${entityName}")`)).toBeVisible();
  });

  test('should handle rapid entity creation', async ({ page }) => {
    // PERFORMANCE OPTIMIZATION: Test both parallel and sequential approaches
    console.log('🚀 Testing rapid entity creation with parallel optimization...');
    const testStartTime = Date.now();
    
    // First test: Parallel API-based creation (should be much faster)
    const parallelEntityNames = [
      helpers.generateUniqueEntityName('Parallel A'),
      helpers.generateUniqueEntityName('Parallel B'),
      helpers.generateUniqueEntityName('Parallel C'),
    ];
    
    console.log('Phase 1: Testing parallel entity creation via API...');
    const parallelStartTime = Date.now();
    
    try {
      const parallelEntities = await helpers.createEntitiesParallel(parallelEntityNames);
      const parallelDuration = Date.now() - parallelStartTime;
      
      // Verify entities are visible in UI
      await entityPage.goto('/entities');
      await helpers.waitForAppReady();
      
      for (const entity of parallelEntities) {
        await expect(page.locator(`:text("${entity.name}")`)).toBeVisible({ timeout: 3000 });
      }
      
      console.log(`✅ Parallel creation completed in ${parallelDuration}ms for ${parallelEntities.length} entities`);
      console.log(`  • Average time per entity (parallel): ${Math.round(parallelDuration / parallelEntities.length)}ms`);
      
    } catch (error) {
      console.error(`❌ Parallel creation failed: ${error}`);
      throw error;
    }
    
    // Second test: Sequential UI-based creation (for comparison and form testing)
    const sequentialEntityNames = [
      helpers.generateUniqueEntityName('Sequential A'),
      helpers.generateUniqueEntityName('Sequential B'),
      helpers.generateUniqueEntityName('Sequential C'),
    ];
    
    console.log('Phase 2: Testing sequential entity creation via UI for comparison...');
    const sequentialStartTime = Date.now();
    
    // Create entities sequentially with enhanced form state isolation
    for (let i = 0; i < sequentialEntityNames.length; i++) {
      const name = sequentialEntityNames[i];
      console.log(`Creating entity ${i + 1}/${sequentialEntityNames.length}: ${name}`);
      
      // Enhanced pre-creation setup to ensure clean state
      await page.waitForLoadState('networkidle');
      await helpers.waitForAppReady();
      
      // Create entity with full form lifecycle management
      await entityPage.createEntity(name);
      
      // Enhanced entity verification with browser-specific timeout and retry logic
      const browserTimeout = ValidationTimeouts.getBrowserEntityVerificationTimeout(page);
      console.log(`  Verifying entity ${i + 1} with browser timeout: ${browserTimeout}ms`);
      
      try {
        await expect(page.locator(`:text("${name}")`)).toBeVisible({ timeout: browserTimeout });
        console.log(`  ✓ Entity ${i + 1} verified successfully`);
      } catch (initialVerificationError) {
        console.log(`  ⚠️  Entity ${i + 1} not immediately visible, trying enhanced verification...`);
        
        // Enhanced verification with UI refresh detection
        await helpers.waitForEntityListRefresh(page);
        
        try {
          await expect(page.locator(`:text("${name}")`)).toBeVisible({ timeout: 3000 });
          console.log(`  ✓ Entity ${i + 1} verified after UI refresh`);
        } catch (retryError) {
          console.log(`  🔄 Performing page reload for entity ${i + 1}...`);
          await page.reload();
          await helpers.waitForAppReady();
          await expect(page.locator(`:text("${name}")`)).toBeVisible({ timeout: browserTimeout });
          console.log(`  ✓ Entity ${i + 1} verified after page reload`);
        }
      }
      
      // Reduced delays for better performance while maintaining stability
      const baseDelay = 200; // Reduced from 300ms
      const progressiveDelay = i * 50; // Reduced from 100ms
      const totalDelay = baseDelay + progressiveDelay;
      
      console.log(`  ✓ Entity ${i + 1} created successfully. Waiting ${totalDelay}ms for state stabilization...`);
      await page.waitForTimeout(totalDelay);
      
      // Additional verification that form state is clean before next iteration
      await expect(entityPage.entityForm).not.toBeAttached();
      await expect(entityPage.createButton).toBeVisible();
      
      // Ensure no error messages are lingering
      if (await entityPage.errorMessage.isVisible().catch(() => false)) {
        console.log(`  Warning: Error message still visible after entity ${i + 1}, clearing...`);
        await entityPage.clearError();
      }
    }
    
    const sequentialDuration = Date.now() - sequentialStartTime;
    
    console.log('Sequential creation sequence completed. Performing final verification...');
    
    // Enhanced final verification with individual entity confirmation
    for (let i = 0; i < sequentialEntityNames.length; i++) {
      const name = sequentialEntityNames[i];
      console.log(`  Final verification ${i + 1}/${sequentialEntityNames.length}: ${name}`);
      
      // More robust entity verification with browser-specific timeouts and retry logic
      const entityLocator = page.locator(`:text("${name}")`);
      const browserTimeout = ValidationTimeouts.getBrowserEntityVerificationTimeout(page);
      
      try {
        // First attempt with browser-specific timeout
        await expect(entityLocator).toBeVisible({ timeout: browserTimeout });
        console.log(`    ✓ Confirmed: ${name} (first attempt)`);
      } catch (firstAttemptError) {
        console.log(`    ⚠️  Entity ${name} not initially visible, trying fallback strategies...`);
        
        // Enhanced fallback verification
        let verified = false;
        
        // Strategy 1: UI refresh
        try {
          await helpers.waitForEntityListRefresh(page);
          await expect(entityLocator).toBeVisible({ timeout: 3000 });
          console.log(`    ✓ Confirmed: ${name} (after UI refresh)`);
          verified = true;
        } catch (refreshError) {
          console.log(`    ❌ UI refresh failed for: ${name}`);
        }
        
        // Strategy 2: Page reload (only if UI refresh failed)
        if (!verified) {
          try {
            console.log(`    🔄 Page reload for: ${name}`);
            await page.reload();
            await helpers.waitForAppReady();
            await expect(entityLocator).toBeVisible({ timeout: browserTimeout });
            console.log(`    ✓ Confirmed: ${name} (after page reload)`);
            verified = true;
          } catch (reloadError) {
            console.log(`    ❌ Page reload failed for: ${name}`);
          }
        }
        
        // If all strategies failed, throw with detailed error
        if (!verified) {
          throw new Error(`Final verification failed for entity "${name}" after all retry strategies`);
        }
      }
    }
    
    // Performance comparison summary
    const totalDuration = Date.now() - testStartTime;
    console.log(`✅ All rapid entity creations verified successfully!`);
    console.log(`📊 Performance Summary:`);
    console.log(`  • Parallel creation: ${parallelStartTime ? Date.now() - parallelStartTime : 0}ms for 3 entities`);
    console.log(`  • Sequential creation: ${sequentialDuration}ms for 3 entities`);
    console.log(`  • Total test time: ${totalDuration}ms`);
    console.log(`  • Parallel speedup: ~${Math.round(sequentialDuration / (parallelStartTime ? Date.now() - parallelStartTime : 1))}x faster`);
  });

  test('should maintain validation consistency across form interactions', async ({ page }) => {
    // Test validation state persistence and consistency
    await entityPage.clickCreateNew();
    
    const validationTimeout = ValidationTimeouts.getBrowserValidationTimeout(page);
    const buttonTimeout = ValidationTimeouts.getBrowserButtonTimeout(page);
    const errorTimeout = ValidationTimeouts.getBrowserErrorTimeout(page);
    
    // Start with valid input
    await entityPage.nameInput.fill('Valid Name');
    await entityPage.nameInput.blur();
    
    // Enhanced wait for initial valid state with browser-specific timing
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const successIndicator = document.querySelector('.validation-success') || document.querySelector('[aria-live="polite"]');
      
      if (!input || !submitBtn) return false;
      
      // For valid input: should have all valid indicators
      const hasValidPattern = /^[a-zA-Z\s]+$/.test(input.value);
      const hasValidClass = input.classList.contains('valid');
      const isEnabled = !submitBtn.disabled;
      const ariaValid = input.getAttribute('aria-invalid') === 'false';
      
      return hasValidPattern && hasValidClass && isEnabled && ariaValid && successIndicator;
    }, { timeout: validationTimeout });
    
    // Verify valid state
    await expect(entityPage.nameInput).toHaveClass(/valid/);
    await expect(entityPage.submitButton).toBeEnabled({ timeout: buttonTimeout });
    await expect(page.locator('.validation-success, [aria-live="polite"]')).toBeVisible();
    
    // Change to invalid input (special characters)
    await entityPage.nameInput.fill('Invalid123');
    
    // Enhanced wait for invalid state transition with browser-specific timing
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const errorMsg = document.querySelector('[data-testid="entity-form-error"], .error-message, [role="alert"]');
      
      if (!input || !submitBtn) return false;
      
      // For invalid input: should have invalid state
      const hasInvalidPattern = !/^[a-zA-Z\s]+$/.test(input.value);
      const hasInvalidClass = input.classList.contains('invalid');
      const isDisabled = submitBtn.disabled;
      const hasPatternError = errorMsg && errorMsg.textContent && errorMsg.textContent.includes('letters');
      const ariaInvalid = input.getAttribute('aria-invalid') === 'true';
      
      return hasInvalidPattern && hasInvalidClass && isDisabled && hasPatternError && ariaInvalid;
    }, { timeout: validationTimeout });
    
    // Should immediately show invalid state
    await expect(entityPage.nameInput).toHaveClass(/invalid/);
    await expect(entityPage.submitButton).toBeDisabled();
    await expect(entityPage.errorMessage).toBeVisible({ timeout: errorTimeout });
    await expect(entityPage.errorMessage).toContainText(/letters and spaces/i);
    
    // Clear and test empty validation
    await entityPage.nameInput.fill('');
    await entityPage.nameInput.blur();
    
    // Enhanced wait for empty field validation with browser-specific timing
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const errorMsg = document.querySelector('[data-testid="entity-form-error"], .error-message, [role="alert"]');
      
      if (!input || !submitBtn) return false;
      
      // For empty input: should show required error
      const isEmpty = input.value.trim() === '';
      const hasInvalidClass = input.classList.contains('invalid');
      const isDisabled = submitBtn.disabled;
      const hasRequiredError = errorMsg && errorMsg.textContent && errorMsg.textContent.includes('required');
      const ariaInvalid = input.getAttribute('aria-invalid') === 'true';
      
      return isEmpty && hasInvalidClass && isDisabled && hasRequiredError && ariaInvalid;
    }, { timeout: validationTimeout });
    
    // Should show required error
    await expect(entityPage.nameInput).toHaveClass(/invalid/);
    await expect(entityPage.errorMessage).toBeVisible({ timeout: errorTimeout });
    await expect(entityPage.errorMessage).toContainText(/required/i);
    
    // Return to valid state
    await entityPage.nameInput.fill('Final Valid Name');
    await entityPage.nameInput.blur();
    
    // Enhanced wait for final validation recovery with browser-specific timing
    await page.waitForFunction(() => {
      const input = document.querySelector('[data-testid="entity-name-input"]') as HTMLInputElement;
      const submitBtn = document.querySelector('[data-testid="entity-submit-button"]') as HTMLButtonElement;
      const errorMsg = document.querySelector('[data-testid="entity-form-error"], .error-message, [role="alert"]');
      const successIndicator = document.querySelector('.validation-success') || document.querySelector('[aria-live="polite"]');
      
      if (!input || !submitBtn) return false;
      
      // For final valid input: should have all valid indicators restored
      const hasValidPattern = /^[a-zA-Z\s]+$/.test(input.value);
      const hasValidClass = input.classList.contains('valid');
      const isEnabled = !submitBtn.disabled;
      const ariaValid = input.getAttribute('aria-invalid') === 'false';
      const noError = !errorMsg || !errorMsg.textContent || errorMsg.style.display === 'none';
      
      return hasValidPattern && hasValidClass && isEnabled && ariaValid && noError && successIndicator;
    }, { timeout: validationTimeout });
    
    // Should return to valid state
    await expect(entityPage.nameInput).toHaveClass(/valid/);
    await expect(entityPage.submitButton).toBeEnabled({ timeout: buttonTimeout });
    await expect(entityPage.errorMessage).not.toBeVisible();
    await expect(page.locator('.validation-success, [aria-live="polite"]')).toBeVisible();
    
    await entityPage.cancelForm();
  });
});