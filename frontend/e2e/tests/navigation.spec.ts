import { test, expect } from '@playwright/test';
import { BasePage, NavigationComponent } from '../fixtures/page-objects';
import { TestHelpers } from '../utils/helpers';

test.describe('Navigation', () => {
  test.beforeEach(async ({ page }) => {
    const helpers = new TestHelpers(page);
    await helpers.setupDialogHandler();
    
    const basePage = new BasePage(page);
    await basePage.goto();
    await helpers.waitForAppReady();
  });

  test('should load homepage successfully', async ({ page }) => {
    await expect(page).toHaveTitle(/SIMILE/);
    await expect(page.locator('h2:has-text("Entity Comparison")')).toBeVisible();
  });

  test('should navigate between all pages', async ({ page }) => {
    const basePage = new BasePage(page);
    
    // Test navigation to entities page
    await basePage.navigateTo('entities');
    await expect(page).toHaveURL(/\/entities/);
    await expect(page.locator('h2:has-text("Entity Management")')).toBeVisible();
    
    // Test navigation to connections page
    await basePage.navigateTo('connections');
    await expect(page).toHaveURL(/\/connections/);
    await expect(page.locator('h2:has-text("Connection Management")')).toBeVisible();
    
    // Test navigation back to home
    await basePage.navigateTo('home');
    await expect(page).toHaveURL(/\/$/);
    await expect(page.locator('h2:has-text("Entity Comparison")')).toBeVisible();
  });

  test('should highlight active navigation item', async ({ page }) => {
    const navigation = new NavigationComponent(page);
    
    // Check home is active initially
    await expect(navigation.homeLink).toHaveClass(/active/);
    
    // Navigate to entities and check active state
    await navigation.entitiesLink.click();
    await page.waitForURL(/\/entities/);
    await expect(navigation.entitiesLink).toHaveClass(/active/);
    await expect(navigation.homeLink).not.toHaveClass(/active/);
    
    // Navigate to connections and check active state
    await navigation.connectionsLink.click();
    await page.waitForURL(/\/connections/);
    await expect(navigation.connectionsLink).toHaveClass(/active/);
    await expect(navigation.entitiesLink).not.toHaveClass(/active/);
  });

  test('should handle browser back/forward navigation', async ({ page }) => {
    await page.goto('/entities');
    await expect(page).toHaveURL(/\/entities/);
    
    await page.goto('/connections');
    await expect(page).toHaveURL(/\/connections/);
    
    // Test browser back button
    await page.goBack();
    await expect(page).toHaveURL(/\/entities/);
    
    // Test browser forward button
    await page.goForward();
    await expect(page).toHaveURL(/\/connections/);
  });

  test('should maintain navigation state on page refresh', async ({ page }) => {
    // Navigate to entities page
    await page.goto('/entities');
    await expect(page).toHaveURL(/\/entities/);
    
    // Refresh the page
    await page.reload();
    
    // Should still be on entities page
    await expect(page).toHaveURL(/\/entities/);
    await expect(page.locator('h2:has-text("Entity Management")')).toBeVisible();
  });

  test('should handle invalid routes gracefully', async ({ page }) => {
    // Navigate to non-existent route
    await page.goto('/nonexistent');
    
    // Should redirect to home page or show 404
    // This depends on your routing implementation
    await page.waitForTimeout(1000);
    const currentUrl = page.url();
    
    // Either redirects to home or stays on the invalid URL with proper handling
    expect(currentUrl).toMatch(/\/(nonexistent)?$/);
  });
});