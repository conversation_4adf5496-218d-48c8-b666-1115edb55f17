import { test, expect } from '@playwright/test';

test('debug validation timing', async ({ page }) => {
  // Navigate to entities page
  await page.goto('/entities');
  await page.waitForLoadState('networkidle');
  
  // Click create new entity
  await page.click('[data-testid="create-new-entity-button"]');
  
  // Wait for form to be visible
  await page.waitForSelector('[data-testid="entity-form"]', { state: 'visible' });
  await page.waitForTimeout(500); // Wait for React component to settle
  
  const input = page.locator('[data-testid="entity-name-input"]');
  
  // Test 1: Type and clear to trigger validation
  console.log('=== Test 1: Typing and clearing ===');
  await input.fill('test');
  await input.fill('');
  await input.blur();
  
  // Wait and check validation state with multiple checkpoints
  await page.waitForTimeout(500);
  console.log('=== Checkpoint 1 (500ms) ===');
  const classList1 = await input.evaluate(el => Array.from(el.classList));
  console.log('- Class list:', classList1);
  
  await page.waitForTimeout(500);
  console.log('=== Checkpoint 2 (1000ms total) ===');
  
  const hasInvalidClass = await input.evaluate(el => el.classList.contains('invalid'));
  const ariaInvalid = await input.getAttribute('aria-invalid');
  const errorVisible = await page.locator('[data-testid="entity-form-error"]').isVisible();
  const className = await input.getAttribute('class');
  
  console.log('After clear:');
  console.log('- Full className attribute:', `"${className}"`);
  console.log('- Has invalid class:', hasInvalidClass);
  console.log('- aria-invalid:', ariaInvalid);
  console.log('- Error message visible:', errorVisible);
  
  if (errorVisible) {
    const errorText = await page.locator('[data-testid="entity-form-error"]').textContent();
    console.log('- Error text:', errorText);
  }
  
  // Test 2: Type valid input
  console.log('\n=== Test 2: Valid input ===');
  await input.fill('Valid Name');
  await input.blur();
  await page.waitForTimeout(1000);
  
  const hasValidClass = await input.evaluate(el => el.classList.contains('valid'));
  const ariaValidAfter = await input.getAttribute('aria-invalid');
  const submitEnabled = await page.locator('[data-testid="entity-submit-button"]').isEnabled();
  const successVisible = await page.locator('.validation-success').isVisible();
  
  console.log('After valid input:');
  console.log('- Has valid class:', hasValidClass);
  console.log('- aria-invalid:', ariaValidAfter);
  console.log('- Submit button enabled:', submitEnabled);
  console.log('- Success indicator visible:', successVisible);
  
  // Test 3: Type invalid characters
  console.log('\n=== Test 3: Invalid characters ===');
  await input.fill('Invalid123');
  await page.waitForTimeout(1000); // Don't blur, test onChange validation
  
  const hasInvalidClass2 = await input.evaluate(el => el.classList.contains('invalid'));
  const ariaInvalid2 = await input.getAttribute('aria-invalid');
  const submitDisabled = await page.locator('[data-testid="entity-submit-button"]').isDisabled();
  const errorVisible2 = await page.locator('[data-testid="entity-form-error"]').isVisible();
  
  console.log('After invalid characters:');
  console.log('- Has invalid class:', hasInvalidClass2);
  console.log('- aria-invalid:', ariaInvalid2);
  console.log('- Submit button disabled:', submitDisabled);
  console.log('- Error message visible:', errorVisible2);
  
  if (errorVisible2) {
    const errorText2 = await page.locator('[data-testid="entity-form-error"]').textContent();
    console.log('- Error text:', errorText2);
  }
  
  // Check class list
  const classList = await input.evaluate(el => Array.from(el.classList));
  console.log('- Full class list:', classList);
});