import { Page } from '@playwright/test';

/**
 * Backend Connectivity Fix for E2E Tests
 * 
 * This module addresses the connectivity and API issues preventing E2E tests
 * from reliably connecting to and communicating with backend services.
 */
export class BackendConnectivityFix {
  private static instance: BackendConnectivityFix;
  private page: Page;
  private baseURL: string;
  
  constructor(page: Page) {
    this.page = page;
    this.baseURL = 'http://localhost:8000/api/v1';
  }
  
  static getInstance(page: Page): BackendConnectivityFix {
    if (!this.instance) {
      this.instance = new BackendConnectivityFix(page);
    }
    return this.instance;
  }
  
  /**
   * Test backend connectivity and validate API endpoints
   */
  async testBackendConnectivity(): Promise<{
    healthy: boolean;
    issues: string[];
    endpoints: Record<string, boolean>;
  }> {
    console.log('🔍 Testing backend connectivity...');
    
    const issues: string[] = [];
    const endpoints: Record<string, boolean> = {};
    
    // Test core endpoints
    const endpointsToTest = [
      { name: 'units', url: '/units/' },
      { name: 'entities', url: '/entities/' },
      { name: 'connections', url: '/connections/' },
      { name: 'health', url: '/health' }
    ];
    
    for (const endpoint of endpointsToTest) {
      try {
        const response = await this.page.request.get(`${this.baseURL}${endpoint.url}`);
        endpoints[endpoint.name] = response.ok();
        
        if (!response.ok()) {
          issues.push(`${endpoint.name} endpoint returned ${response.status()}`);
        } else {
          console.log(`  ✅ ${endpoint.name}: ${response.status()}`);
        }
      } catch (error) {
        endpoints[endpoint.name] = false;
        issues.push(`${endpoint.name} endpoint failed: ${error}`);
        console.log(`  ❌ ${endpoint.name}: ${error}`);
      }
    }
    
    const healthy = Object.values(endpoints).every(status => status);
    console.log(`🔍 Backend connectivity: ${healthy ? 'HEALTHY' : 'ISSUES FOUND'}`);
    
    return { healthy, issues, endpoints };
  }
  
  /**
   * Fix entity name generation to prevent duplicates
   * Backend requires pattern: ^[a-zA-Z\s]+$ (only letters and spaces)
   */
  generateUniqueEntityName(prefix: string = 'Test'): string {
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    
    // Sanitize prefix to remove any numbers or special characters
    const sanitizedPrefix = prefix.replace(/[^a-zA-Z]/g, '').substring(0, 6) || 'Test';
    
    // Convert worker ID to letter
    const workerLetter = String.fromCharCode(65 + parseInt(workerId)); // 0->A, 1->B, etc.
    
    // Use a more unique counter that increments for each call
    if (!this.nameCounter) {
      this.nameCounter = 0;
    }
    this.nameCounter++;
    
    // Convert counter to letters (more unique than timestamp)
    const counterStr = this.nameCounter.toString().padStart(3, '0').slice(-3); // Last 3 digits
    const counterLetters = counterStr.split('').map(digit => 
      String.fromCharCode(65 + parseInt(digit)) // 0->A, 1->B, ..., 9->J
    ).join('');
    
    // Add random component for extra uniqueness
    const randomLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
    
    // Create name with pattern: "Test W A ABC D" (prefix + W + worker + counter + random)
    const uniqueName = `${sanitizedPrefix} W ${workerLetter} ${counterLetters} ${randomLetter}`;
    
    // Ensure it's within 20 characters limit
    const sanitized = uniqueName.substring(0, 20).trim();
    
    console.log(`Generated unique entity name: ${sanitized} (from prefix: ${prefix}, counter: ${this.nameCounter})`);
    return sanitized;
  }
  
  private nameCounter: number = 0;
  
  /**
   * Enhanced entity creation with proper error handling
   */
  async createEntitySafely(name: string): Promise<{ success: boolean; entity?: any; error?: string }> {
    try {
      console.log(`Creating entity: ${name}`);
      
      const response = await this.page.request.post(`${this.baseURL}/entities/`, {
        data: {
          name: name,
          unit_id: 1 // Default to Length unit
        }
      });
      
      if (response.ok()) {
        const entity = await response.json();
        console.log(`  ✅ Entity created: ${entity.name} (ID: ${entity.id})`);
        return { success: true, entity };
      } else {
        const errorText = await response.text();
        console.log(`  ❌ Entity creation failed: ${response.status()} - ${errorText}`);
        return { success: false, error: `${response.status()}: ${errorText}` };
      }
    } catch (error) {
      console.log(`  ❌ Entity creation error: ${error}`);
      return { success: false, error: error.toString() };
    }
  }
  
  /**
   * Enhanced entity deletion with proper error handling
   */
  async deleteEntitySafely(entityId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`Deleting entity ID: ${entityId}`);
      
      const response = await this.page.request.delete(`${this.baseURL}/entities/${entityId}`);
      
      if (response.ok()) {
        console.log(`  ✅ Entity deleted: ${entityId}`);
        return { success: true };
      } else {
        const errorText = await response.text();
        console.log(`  ❌ Entity deletion failed: ${response.status()} - ${errorText}`);
        return { success: false, error: `${response.status()}: ${errorText}` };
      }
    } catch (error) {
      console.log(`  ❌ Entity deletion error: ${error}`);
      return { success: false, error: error.toString() };
    }
  }
  
  /**
   * Get all entities from the backend
   */
  async getAllEntities(): Promise<any[]> {
    try {
      const response = await this.page.request.get(`${this.baseURL}/entities/`);
      if (response.ok()) {
        return await response.json();
      } else {
        console.error(`Failed to get entities: ${response.status()}`);
        return [];
      }
    } catch (error) {
      console.error(`Error getting entities: ${error}`);
      return [];
    }
  }
  
  /**
   * Enhanced cleanup that actually works
   */
  async performDatabaseCleanup(): Promise<{
    entitiesDeleted: number;
    errors: string[];
    duration: number;
  }> {
    const startTime = Date.now();
    console.log('🧹 Starting enhanced database cleanup...');
    
    let entitiesDeleted = 0;
    const errors: string[] = [];
    
    try {
      // Get all entities
      const entities = await this.getAllEntities();
      console.log(`  Found ${entities.length} total entities`);
      
      // Filter test entities (those that look like test entities)
      const testEntityPatterns = [
        /^Test/i,
        /^BatchTest/i,
        /^PerfTest/i,
        /^Worker/i,
        /^Multi/i,
        /^Overall/i,
        /^Monitor/i,
        /^ErrorTest/i
      ];
      
      const testEntities = entities.filter(entity => 
        testEntityPatterns.some(pattern => pattern.test(entity.name))
      );
      
      console.log(`  Identified ${testEntities.length} test entities to delete`);
      
      // Delete test entities in small batches
      const batchSize = 3; // Small batches to avoid overwhelming the backend
      for (let i = 0; i < testEntities.length; i += batchSize) {
        const batch = testEntities.slice(i, i + batchSize);
        
        for (const entity of batch) {
          const result = await this.deleteEntitySafely(entity.id);
          if (result.success) {
            entitiesDeleted++;
          } else {
            errors.push(`Failed to delete ${entity.name}: ${result.error}`);
          }
        }
        
        // Wait between batches to allow backend to process
        if (i + batchSize < testEntities.length) {
          await this.page.waitForTimeout(200);
        }
      }
      
      const duration = Date.now() - startTime;
      console.log(`🧹 Enhanced cleanup complete: ${entitiesDeleted} entities deleted in ${duration}ms`);
      
      if (errors.length > 0) {
        console.log(`  ⚠️  ${errors.length} errors occurred during cleanup`);
        errors.forEach(error => console.log(`    ${error}`));
      }
      
      return { entitiesDeleted, errors, duration };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Enhanced cleanup failed after ${duration}ms: ${error}`);
      errors.push(error.toString());
      return { entitiesDeleted, errors, duration };
    }
  }
  
  /**
   * Create multiple entities in sequence (not parallel to avoid duplicates)
   */
  async createEntitiesSequentially(names: string[]): Promise<{
    successful: any[];
    failed: string[];
    duration: number;
  }> {
    const startTime = Date.now();
    console.log(`🚀 Creating ${names.length} entities sequentially...`);
    
    const successful: any[] = [];
    const failed: string[] = [];
    
    for (const name of names) {
      const result = await this.createEntitySafely(name);
      if (result.success && result.entity) {
        successful.push(result.entity);
      } else {
        failed.push(`${name}: ${result.error}`);
      }
      
      // Brief pause between creations to avoid race conditions
      await this.page.waitForTimeout(100);
    }
    
    const duration = Date.now() - startTime;
    console.log(`🚀 Sequential creation complete: ${successful.length} successful, ${failed.length} failed in ${duration}ms`);
    
    return { successful, failed, duration };
  }
  
  /**
   * Test API rate limiting and connection stability
   */
  async testAPIStability(): Promise<{
    stable: boolean;
    averageResponseTime: number;
    errors: number;
    requests: number;
  }> {
    console.log('🔍 Testing API stability...');
    
    const requests = 10;
    let errors = 0;
    const responseTimes: number[] = [];
    
    for (let i = 0; i < requests; i++) {
      const start = Date.now();
      try {
        const response = await this.page.request.get(`${this.baseURL}/units/`);
        const responseTime = Date.now() - start;
        responseTimes.push(responseTime);
        
        if (!response.ok()) {
          errors++;
        }
      } catch (error) {
        errors++;
        responseTimes.push(5000); // Assume 5s for failed requests
      }
      
      await this.page.waitForTimeout(100); // Rate limiting
    }
    
    const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const stable = errors < requests * 0.1; // Less than 10% error rate
    
    console.log(`🔍 API stability: ${stable ? 'STABLE' : 'UNSTABLE'} (${errors}/${requests} errors, avg ${averageResponseTime.toFixed(0)}ms)`);
    
    return { stable, averageResponseTime, errors, requests };
  }
  
  /**
   * Comprehensive backend health check
   */
  async performHealthCheck(): Promise<{
    healthy: boolean;
    connectivity: boolean;
    stability: boolean;
    cleanup: boolean;
    issues: string[];
  }> {
    console.log('🏥 Performing comprehensive backend health check...');
    
    const issues: string[] = [];
    
    // Test connectivity
    const connectivityResult = await this.testBackendConnectivity();
    const connectivity = connectivityResult.healthy;
    if (!connectivity) {
      issues.push(...connectivityResult.issues);
    }
    
    // Test stability
    const stabilityResult = await this.testAPIStability();
    const stability = stabilityResult.stable;
    if (!stability) {
      issues.push(`API unstable: ${stabilityResult.errors}/${stabilityResult.requests} failed requests`);
    }
    
    // Test cleanup capability
    const cleanupResult = await this.performDatabaseCleanup();
    const cleanup = cleanupResult.errors.length === 0;
    if (!cleanup) {
      issues.push(...cleanupResult.errors);
    }
    
    const healthy = connectivity && stability && cleanup;
    
    console.log(`🏥 Backend health check: ${healthy ? 'HEALTHY' : 'ISSUES FOUND'}`);
    if (issues.length > 0) {
      console.log(`  Issues: ${issues.length}`);
      issues.forEach(issue => console.log(`    - ${issue}`));
    }
    
    return { healthy, connectivity, stability, cleanup, issues };
  }
}