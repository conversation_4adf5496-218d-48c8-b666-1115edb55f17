import { defineConfig, devices } from '@playwright/test';

/**
 * Optimized Playwright Configuration for 3-Worker Parallel Execution
 * 
 * Key optimizations:
 * - Restored 3-worker parallel execution with proper isolation
 * - Enhanced timeout configurations for browser-specific performance
 * - Improved retry logic with exponential backoff
 * - Worker-specific database isolation
 * - Optimized test sharding and distribution
 * 
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './e2e/tests',
  
  // Optimized global timeouts with better resource management
  timeout: 90 * 1000,            // 90 seconds per test (reduced from 120s)
  globalTimeout: 90 * 60 * 1000, // 90 minutes global timeout (reduced from 120m)
  
  // Phase C: Restored 3-worker parallel execution with enhanced isolation
  fullyParallel: true,
  workers: process.env.CI ? 2 : 3, // Restored 3 workers for local dev, 2 for CI
  
  // Enhanced test distribution and isolation
  forbidOnly: !!(process.env as any).CI,
  retries: process.env.CI ? 2 : 1, // More retries in CI environment
  maxFailures: process.env.CI ? 15 : 10, // Reduced max failures for faster feedback
  
  // Optimized test execution parameters
  outputDir: './test-results',
  fullyParallel: true,
  
  // Enhanced reporting with better performance insights
  reporter: [
    ['./e2e/utils/progress-reporter.ts'], // Custom progress reporter
    ['list', { printSteps: false }], // Basic list reporter as backup
    ['html', { outputFolder: 'playwright-report', open: 'never' }], // HTML report
    ['json', { outputFile: 'test-results.json' }], // JSON for analysis
    ['junit', { outputFile: 'junit-results.xml' }], // JUnit for CI integration
  ],
  
  use: {
    baseURL: 'http://localhost:3000',
    
    // Optimized timeouts for better stability
    actionTimeout: 8 * 1000,      // 8 seconds for actions (increased from 5s)
    navigationTimeout: 15 * 1000, // 15 seconds for navigation (increased from 10s)
    
    // Enhanced assertion timeouts
    expect: {
      timeout: 12 * 1000          // 12 seconds for assertions (increased from 10s)
    },
    
    // Backend API configuration with enhanced headers
    extraHTTPHeaders: {
      'x-test-backend-url': 'http://localhost:8000',
      'x-test-worker-id': process.env.TEST_WORKER_INDEX || '0',
      'x-test-isolation': 'enabled'
    },
    
    // Enhanced error reporting and debugging
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // Improved test isolation settings
    ignoreHTTPSErrors: true,
    bypassCSP: true,
  },
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // Optimized Chromium settings for fast, stable execution
        actionTimeout: 8 * 1000,      // Standard timeout for Chrome
        navigationTimeout: 15 * 1000, // Standard navigation timeout
        expect: {
          timeout: 12 * 1000          // Standard assertion timeout
        },
        // Chrome-specific optimizations
        launchOptions: {
          args: [
            '--disable-web-security',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-dev-shm-usage',
            '--no-sandbox'
          ],
        },
      },
    },
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        // Optimized Firefox settings for better stability
        actionTimeout: 10 * 1000,     // Slightly longer for Firefox
        navigationTimeout: 18 * 1000, // Firefox needs more time for navigation
        expect: {
          timeout: 14 * 1000          // Extended assertions for Firefox
        },
        // Firefox-specific optimizations
        launchOptions: {
          firefoxUserPrefs: {
            'dom.webnotifications.enabled': false,
            'dom.push.enabled': false,
            'media.navigator.permission.disabled': true,
          },
        },
      },
    },
    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        // Enhanced WebKit settings for form interactions and stability
        actionTimeout: 15 * 1000,     // Extended timeout for WebKit form actions
        navigationTimeout: 20 * 1000, // Extended navigation timeout for WebKit
        expect: {
          timeout: 16 * 1000          // Extended assertion timeout for WebKit
        },
        // WebKit-specific optimizations
        launchOptions: {
          args: [
            '--disable-web-security',
            '--allow-running-insecure-content',
          ],
        },
      },
    },
  ],
  webServer: {
    command: 'npm start',
    url: 'http://localhost:3000',
    reuseExistingServer: !(process.env as any).CI,
    timeout: 120 * 1000,
    // Enhanced web server settings for better stability
    ignoreHTTPSErrors: true,
    stdout: 'pipe',
    stderr: 'pipe',
  },
  
  // Enhanced test execution settings
  metadata: {
    optimizationVersion: 'C.1',
    workerCapacity: '3-worker-parallel',
    isolationStrategy: 'worker-database-isolation',
  },
});