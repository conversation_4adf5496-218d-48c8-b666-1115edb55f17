#!/bin/bash
set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_FILE="test-backend.log"

# Function to log and display messages
log_message() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Initialize log file
echo "SIMILE Backend Tests - $(date)" > "$LOG_FILE"
echo "=================================" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

log_message "${BLUE}🐍 SIMILE Backend Tests${NC}"
log_message "======================="
log_message ""

# Change to backend directory
cd backend

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    log_message "${RED}❌ Backend virtual environment not found!${NC}"
    log_message "${YELLOW}   Please run: python3.11 -m venv venv && source venv/bin/activate && pip install -r requirements.txt${NC}"
    exit 1
fi

# Activate virtual environment
log_message "${YELLOW}🔧 Activating virtual environment...${NC}"
source venv/bin/activate

# Verify virtual environment is activated
if [[ -z "$VIRTUAL_ENV" ]]; then
    log_message "${RED}❌ Failed to activate virtual environment!${NC}"
    exit 1
fi

log_message "${GREEN}✅ Using virtual environment: $VIRTUAL_ENV${NC}"
log_message ""

# Check if required services are running
log_message "${BLUE}🔍 Checking required services...${NC}"
if ! python scripts/check_test_services.py 2>&1 | tee -a "../$LOG_FILE"; then
    log_message "${RED}❌ Backend services not accessible${NC}"
    log_message "${YELLOW}   Please run: docker-compose -f docker-compose.dev.yml up -d${NC}"
    log_message "${YELLOW}   Or: podman-compose up -d${NC}"
    deactivate
    exit 1
fi

log_message "${GREEN}✅ Backend services are running${NC}"
log_message ""

# Set environment variables
export DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test"
export TEST_DATABASE_HOST="localhost"
export PYTHONPATH="."

# Setup test database
log_message "${YELLOW}🗄️  Setting up test database...${NC}"
if ! python scripts/test_db_setup.py 2>&1 | tee -a "../$LOG_FILE"; then
    log_message "${RED}❌ Failed to setup test database${NC}"
    deactivate
    exit 1
fi

log_message "${GREEN}✅ Test database setup complete${NC}"
log_message ""

# Run linting
log_message "${YELLOW}🧹 Running linting...${NC}"
if ! make lint 2>&1 | tee -a "../$LOG_FILE"; then
    log_message "${YELLOW}⚠️  Linting issues found${NC}"
fi

log_message ""

# Run type checking
log_message "${YELLOW}🔍 Running type checking...${NC}"
if ! make typecheck 2>&1 | tee -a "../$LOG_FILE"; then
    log_message "${YELLOW}⚠️  Type checking issues found${NC}"
fi

log_message ""

# Run tests
log_message "${YELLOW}🧪 Running tests...${NC}"
if pytest -v 2>&1 | tee -a "../$LOG_FILE"; then
    TEST_EXIT_CODE=0
    log_message "${GREEN}✅ All tests passed!${NC}"
else
    TEST_EXIT_CODE=1
    log_message "${RED}❌ Some tests failed!${NC}"
fi

log_message ""

# Run coverage report
log_message "${YELLOW}📊 Generating coverage report...${NC}"
if make coverage 2>&1 | tee -a "../$LOG_FILE"; then
    log_message "${GREEN}✅ Coverage report generated${NC}"
else
    log_message "${YELLOW}⚠️  Coverage report generation failed${NC}"
fi

log_message ""

# Cleanup test database
log_message "${YELLOW}🧹 Cleaning up test database...${NC}"
python scripts/test_db_teardown.py 2>&1 | tee -a "../$LOG_FILE"

# Deactivate virtual environment
deactivate

# Return to project root
cd ..

log_message ""
log_message "Backend test run completed at $(date)"
log_message "Test results saved to: $LOG_FILE"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    log_message "${GREEN}🎉 Backend tests completed successfully!${NC}"
else
    log_message "${RED}💥 Backend tests failed!${NC}"
    log_message "${YELLOW}Check $LOG_FILE for details${NC}"
fi

exit $TEST_EXIT_CODE